#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to rename Google Drive image files to include product name prefix.

This script will:
1. Connect to Google Drive
2. Find the 'images' folder and all product subfolders
3. For each product folder, rename files from "image_<index>.<ext>" to "<product_name>_<index>.<ext>"
4. Provide detailed logging of all rename operations
5. Skip files that are already properly named
"""

import os
import re
from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
import time

# Configuration
GOOGLE_CREDS_PATH = os.getenv("GOOGLE_CREDS_PATH", "./cactusscrape-5d1e00101c40-google-creds.json")
DRIVE_FOLDER_ID = "1lXAYqTt8dfZzFyEQz0ttqZX1rVfEk2j_"
DELAY_BETWEEN_OPERATIONS = 0.5  # seconds to avoid rate limiting

def setup_google_drive():
    """Authenticate and set up Google Drive client."""
    scopes = ["https://www.googleapis.com/auth/drive"]
    try:
        creds = Credentials.from_service_account_file(GOOGLE_CREDS_PATH, scopes=scopes)
        drive_service = build("drive", "v3", credentials=creds)
        print("✓ Google Drive client initialized successfully")
        return drive_service
    except Exception as e:
        print(f"✗ Error setting up Google Drive: {e}")
        return None

def is_old_format(filename):
    """Check if filename follows the old 'image_<index>.<ext>' format."""
    pattern = r'^image_\d+\.[a-zA-Z0-9]+$'
    return bool(re.match(pattern, filename))

def extract_index_and_extension(filename):
    """Extract index and extension from old format filename."""
    # Pattern: image_<index>.<ext>
    match = re.match(r'^image_(\d+)\.([a-zA-Z0-9]+)$', filename)
    if match:
        return match.group(1), match.group(2)
    return None, None

def find_images_folder(drive_service):
    """Find the main 'images' folder in Google Drive."""
    try:
        query = f"name='images' and '{DRIVE_FOLDER_ID}' in parents and mimeType='application/vnd.google-apps.folder' and trashed=false"
        results = drive_service.files().list(q=query, fields="files(id, name)").execute()
        files = results.get('files', [])

        if files:
            print(f"✓ Found images folder: {files[0]['id']}")
            return files[0]['id']
        else:
            print("✗ Images folder not found in Google Drive")
            return None
    except Exception as e:
        print(f"✗ Error finding images folder: {e}")
        return None

def get_product_folders(drive_service, images_folder_id):
    """Get all product folders within the images folder with pagination."""
    try:
        all_folders = []
        page_token = None
        page_count = 0

        while True:
            page_count += 1
            query = f"'{images_folder_id}' in parents and mimeType='application/vnd.google-apps.folder' and trashed=false"

            # Build request with pagination
            request_params = {
                'q': query,
                'fields': "nextPageToken, files(id, name)",
                'pageSize': 100  # Maximum allowed by Google Drive API
            }

            if page_token:
                request_params['pageToken'] = page_token

            print(f"  Fetching page {page_count} of product folders...")
            results = drive_service.files().list(**request_params).execute()

            folders = results.get('files', [])
            all_folders.extend(folders)

            print(f"  Found {len(folders)} folders on page {page_count}")

            # Check if there are more pages
            page_token = results.get('nextPageToken')
            if not page_token:
                break

            time.sleep(0.1)  # Small delay between pages

        print(f"✓ Found {len(all_folders)} total product folders across {page_count} pages")
        return all_folders

    except Exception as e:
        print(f"✗ Error getting product folders: {e}")
        return []

def get_files_in_folder(drive_service, folder_id):
    """Get all files in a specific folder with pagination."""
    try:
        all_files = []
        page_token = None

        while True:
            query = f"'{folder_id}' in parents and trashed=false and mimeType!='application/vnd.google-apps.folder'"

            # Build request with pagination
            request_params = {
                'q': query,
                'fields': "nextPageToken, files(id, name)",
                'pageSize': 100  # Maximum allowed by Google Drive API
            }

            if page_token:
                request_params['pageToken'] = page_token

            results = drive_service.files().list(**request_params).execute()

            files = results.get('files', [])
            all_files.extend(files)

            # Check if there are more pages
            page_token = results.get('nextPageToken')
            if not page_token:
                break

            time.sleep(0.1)  # Small delay between pages

        return all_files

    except Exception as e:
        print(f"✗ Error getting files in folder: {e}")
        return []

def rename_file_in_drive(drive_service, file_id, new_name):
    """Rename a file in Google Drive."""
    try:
        file_metadata = {'name': new_name}
        drive_service.files().update(fileId=file_id, body=file_metadata).execute()
        return True
    except Exception as e:
        print(f"    ✗ Error renaming file: {e}")
        return False

def check_file_exists_in_folder(drive_service, folder_id, filename):
    """Check if a file with the given name already exists in the folder."""
    try:
        query = f"name='{filename}' and '{folder_id}' in parents and trashed=false"
        results = drive_service.files().list(q=query, fields="files(id, name)").execute()
        files = results.get('files', [])
        return len(files) > 0
    except Exception as e:
        print(f"    ✗ Error checking file existence: {e}")
        return True  # Assume it exists to be safe

def rename_images_in_product_folder(drive_service, folder_id, product_name):
    """Rename all images in a product folder to include product name prefix."""
    renamed_count = 0
    skipped_count = 0
    error_count = 0

    print(f"\n📁 Processing folder: {product_name}")

    # Get all files in the product folder
    files = get_files_in_folder(drive_service, folder_id)

    if not files:
        print(f"  ⚠ No files found in folder")
        return 0, 0, 0

    print(f"  Found {len(files)} files")

    for file_info in files:
        filename = file_info['name']
        file_id = file_info['id']

        # Check if file is in old format
        if not is_old_format(filename):
            print(f"    ⏭ Skipping '{filename}' (already in new format or not an image)")
            skipped_count += 1
            continue

        # Extract index and extension
        index, extension = extract_index_and_extension(filename)
        if not index or not extension:
            print(f"    ✗ Could not parse '{filename}'")
            error_count += 1
            continue

        # Generate new filename
        new_filename = f"{product_name}_{index}.{extension}"

        # Check if new filename already exists
        if check_file_exists_in_folder(drive_service, folder_id, new_filename):
            print(f"    ⚠ Target file '{new_filename}' already exists, skipping")
            skipped_count += 1
            continue

        # Perform the rename
        print(f"    🔄 Renaming: '{filename}' → '{new_filename}'")
        if rename_file_in_drive(drive_service, file_id, new_filename):
            print(f"    ✓ Successfully renamed")
            renamed_count += 1
        else:
            error_count += 1

        # Add delay to avoid rate limiting
        time.sleep(DELAY_BETWEEN_OPERATIONS)

    return renamed_count, skipped_count, error_count

def scan_and_rename_all_drive_images(drive_service):
    """Scan all product folders in Google Drive and rename images."""
    print("🔄 Starting Google Drive image renaming process...")

    # Find the main images folder
    images_folder_id = find_images_folder(drive_service)
    if not images_folder_id:
        return

    # Get all product folders
    product_folders = get_product_folders(drive_service, images_folder_id)
    if not product_folders:
        print("⚠ No product folders found in images directory")
        return

    print(f"📊 Found {len(product_folders)} product folders")

    total_renamed = 0
    total_skipped = 0
    total_errors = 0

    for i, folder_info in enumerate(product_folders, 1):
        folder_name = folder_info['name']
        folder_id = folder_info['id']

        print(f"\n[{i}/{len(product_folders)}] Processing: {folder_name}")

        renamed, skipped, errors = rename_images_in_product_folder(drive_service, folder_id, folder_name)

        total_renamed += renamed
        total_skipped += skipped
        total_errors += errors

        print(f"  📈 Results: {renamed} renamed, {skipped} skipped, {errors} errors")

        # Add delay between folders
        time.sleep(DELAY_BETWEEN_OPERATIONS)

    # Final summary
    print(f"\n{'='*60}")
    print(f"🎉 GOOGLE DRIVE RENAMING COMPLETE!")
    print(f"📊 Final Summary:")
    print(f"   ✓ Files renamed: {total_renamed}")
    print(f"   ⏭ Files skipped: {total_skipped}")
    print(f"   ✗ Errors: {total_errors}")
    print(f"   📁 Folders processed: {len(product_folders)}")
    print(f"{'='*60}")

def preview_drive_changes(drive_service):
    """Preview what changes would be made without actually renaming files."""
    print("👀 PREVIEW MODE - No files will be renamed")

    # Find the main images folder
    images_folder_id = find_images_folder(drive_service)
    if not images_folder_id:
        return

    # Get all product folders
    product_folders = get_product_folders(drive_service, images_folder_id)
    if not product_folders:
        print("⚠ No product folders found in images directory")
        return

    total_to_rename = 0

    for folder_info in product_folders:
        folder_name = folder_info['name']
        folder_id = folder_info['id']

        files = get_files_in_folder(drive_service, folder_id)
        files_to_rename = [f for f in files if is_old_format(f['name'])]

        if files_to_rename:
            print(f"\n📁 {folder_name}:")
            for file_info in files_to_rename:
                filename = file_info['name']
                index, extension = extract_index_and_extension(filename)
                if index and extension:
                    new_filename = f"{folder_name}_{index}.{extension}"
                    print(f"    '{filename}' → '{new_filename}'")
                    total_to_rename += 1

    print(f"\n📊 Total files that would be renamed: {total_to_rename}")

if __name__ == "__main__":
    import sys

    print("🚀 Google Drive Image Renaming Script")
    print("=" * 40)

    # Setup Google Drive
    drive_service = setup_google_drive()
    if not drive_service:
        print("❌ Failed to connect to Google Drive. Exiting.")
        sys.exit(1)

    if len(sys.argv) > 1 and sys.argv[1] == "--preview":
        preview_drive_changes(drive_service)
    else:
        print("This script will rename image files in Google Drive from 'image_<index>.<ext>' to '<product_name>_<index>.<ext>'")
        print("Run with --preview to see what changes would be made without actually renaming files")
        print()

        response = input("Do you want to proceed with renaming? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            scan_and_rename_all_drive_images(drive_service)
        else:
            print("❌ Operation cancelled")
