import os
import time
import requests
import pandas as pd
from urllib.parse import urlparse
import re
from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
import datetime

# Configuration
GOOGLE_CREDS_PATH = os.getenv("GOOGLE_CREDS_PATH", "./cactusscrape-5d1e00101c40-google-creds.json")
DRIVE_FOLDER_ID = "1lXAYqTt8dfZzFyEQz0ttqZX1rVfEk2j_"
IMAGES_BASE_DIR = "images"
DELAY_BETWEEN_DOWNLOADS = 2  # seconds
DELAY_BETWEEN_UPLOADS = 1    # seconds
MAX_RETRIES = 3

def setup_google_drive():
    """Authenticate and set up Google Drive client."""
    scopes = ["https://www.googleapis.com/auth/drive"]
    try:
        creds = Credentials.from_service_account_file(GOOGLE_CREDS_PATH, scopes=scopes)
        drive_service = build("drive", "v3", credentials=creds)
        print("✓ Google Drive client initialized successfully")
        return drive_service
    except Exception as e:
        print(f"✗ Error setting up Google Drive: {e}")
        return None

def sanitize_filename(name):
    """Sanitize a string to be used as a filename or folder name."""
    # Remove HTML tags
    name = re.sub(r'<[^>]+>', '', name)
    # Replace invalid characters with underscores
    name = re.sub(r'[<>:"/\\|?*]', '_', name)
    # Remove extra whitespace and replace with underscores
    name = re.sub(r'\s+', '_', name.strip())
    # Limit length
    return name[:100] if len(name) > 100 else name

def download_image(url, filepath, retries=MAX_RETRIES):
    """Download an image from URL to filepath with retry logic."""
    for attempt in range(retries):
        try:
            print(f"    Downloading: {url}")
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(url, headers=headers, timeout=30, stream=True)
            response.raise_for_status()

            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(filepath), exist_ok=True)

            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            print(f"    ✓ Downloaded: {os.path.basename(filepath)}")
            return True

        except Exception as e:
            print(f"    ✗ Attempt {attempt + 1} failed: {e}")
            if attempt < retries - 1:
                time.sleep(2 ** attempt)  # Exponential backoff

    print(f"    ✗ Failed to download after {retries} attempts")
    return False

def get_image_extension(url):
    """Extract file extension from URL."""
    parsed = urlparse(url)
    path = parsed.path.lower()

    # Common image extensions
    for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
        if ext in path:
            return ext

    # Default to .jpg if no extension found
    return '.jpg'

def create_drive_folder(drive_service, folder_name, parent_folder_id):
    """Create a folder in Google Drive and return its ID."""
    try:
        # Check if folder already exists
        query = f"name='{folder_name}' and '{parent_folder_id}' in parents and mimeType='application/vnd.google-apps.folder' and trashed=false"
        results = drive_service.files().list(q=query, fields="files(id, name)").execute()
        files = results.get('files', [])

        if files:
            print(f"    ✓ Folder '{folder_name}' already exists")
            return files[0]['id']

        # Create new folder
        file_metadata = {
            'name': folder_name,
            'mimeType': 'application/vnd.google-apps.folder',
            'parents': [parent_folder_id]
        }

        folder = drive_service.files().create(body=file_metadata, fields='id').execute()
        print(f"    ✓ Created folder '{folder_name}'")
        return folder.get('id')

    except Exception as e:
        print(f"    ✗ Error creating folder '{folder_name}': {e}")
        return None

def upload_image_to_drive(drive_service, local_path, drive_folder_id, filename):
    """Upload an image file to Google Drive."""
    try:
        # Check if file already exists
        query = f"name='{filename}' and '{drive_folder_id}' in parents and trashed=false"
        results = drive_service.files().list(q=query, fields="files(id, name)").execute()
        files = results.get('files', [])

        if files:
            print(f"      ✓ File '{filename}' already exists in Drive")
            return files[0]['id']

        file_metadata = {
            'name': filename,
            'parents': [drive_folder_id]
        }

        media = MediaFileUpload(local_path, resumable=True)
        file = drive_service.files().create(
            body=file_metadata,
            media_body=media,
            fields='id'
        ).execute()

        print(f"      ✓ Uploaded '{filename}' to Drive")
        return file.get('id')

    except Exception as e:
        print(f"      ✗ Error uploading '{filename}': {e}")
        return None

def get_drive_folder_link(folder_id):
    """Generate a shareable Google Drive folder link."""
    return f"https://drive.google.com/drive/folders/{folder_id}"

def upload_csv_to_drive(drive_service, csv_file, folder_id):
    """Upload CSV file to Google Drive."""
    try:
        filename = os.path.basename(csv_file)

        # Check if file already exists
        query = f"name='{filename}' and '{folder_id}' in parents and trashed=false"
        results = drive_service.files().list(q=query, fields="files(id, name)").execute()
        files = results.get('files', [])

        if files:
            print(f"✓ CSV '{filename}' already exists in Drive")
            return files[0]['id']

        file_metadata = {
            'name': filename,
            'parents': [folder_id]
        }

        media = MediaFileUpload(csv_file, mimetype='text/csv')
        file = drive_service.files().create(
            body=file_metadata,
            media_body=media,
            fields='id'
        ).execute()

        print(f"✓ Uploaded CSV '{filename}' to Drive")
        return file.get('id')

    except Exception as e:
        print(f"✗ Error uploading CSV: {e}")
        return None

def convert_csv_to_sheet(drive_service, file_id, sheet_name):
    """Convert a CSV file on Drive to a Google Sheet."""
    try:
        file_metadata = {
            'name': sheet_name,
            'mimeType': 'application/vnd.google-apps.spreadsheet'
        }

        updated_file = drive_service.files().copy(
            fileId=file_id,
            body=file_metadata,
            fields='id, name'
        ).execute()

        print(f"✓ Converted to Google Sheet: {updated_file['name']}")
        return updated_file['id']

    except Exception as e:
        print(f"✗ Error converting CSV to Sheet: {e}")
        return None

def process_product_images(csv_file):
    """Main function to process all product images."""
    print(f"\n=== STARTING IMAGE PROCESSING ===")
    print(f"Input CSV: {csv_file}")

    # Setup Google Drive
    drive_service = setup_google_drive()
    if not drive_service:
        return None

    # Read CSV
    try:
        df = pd.read_csv(csv_file)
        print(f"✓ Loaded {len(df)} products from CSV")
    except Exception as e:
        print(f"✗ Error reading CSV: {e}")
        return None

    # Create main images folder in Drive
    images_folder_id = create_drive_folder(drive_service, "images", DRIVE_FOLDER_ID)
    if not images_folder_id:
        print("✗ Failed to create main images folder in Drive")
        return None

    # Process each product
    results = []
    total_products = len(df)

    for index, row in df.iterrows():
        product_name = sanitize_filename(row['Title'])
        image_urls = row['Image_URLs'].split(',') if pd.notna(row['Image_URLs']) else []

        print(f"\n[{index + 1}/{total_products}] Processing: {product_name}")
        print(f"  Found {len(image_urls)} images")

        if not image_urls or (len(image_urls) == 1 and not image_urls[0].strip()):
            print("  ⚠ No images found, skipping")
            results.append({**row.to_dict(), 'Google_Drive_Link': ''})
            continue

        # Create local product directory
        product_dir = os.path.join(IMAGES_BASE_DIR, product_name)
        os.makedirs(product_dir, exist_ok=True)

        # Create Google Drive folder for this product
        drive_product_folder_id = create_drive_folder(drive_service, product_name, images_folder_id)
        if not drive_product_folder_id:
            print(f"  ✗ Failed to create Drive folder for {product_name}")
            results.append({**row.to_dict(), 'Google_Drive_Link': ''})
            continue

        # Download and upload each image
        downloaded_count = 0
        for i, image_url in enumerate(image_urls):
            image_url = image_url.strip()
            if not image_url:
                continue

            # Generate filename
            extension = get_image_extension(image_url)
            filename = f"image_{i+1}{extension}"
            local_path = os.path.join(product_dir, filename)

            # Skip if already downloaded
            if os.path.exists(local_path):
                print(f"    ✓ Image {i+1} already exists locally")
                downloaded_count += 1
            else:
                # Download image
                if download_image(image_url, local_path):
                    downloaded_count += 1
                    time.sleep(DELAY_BETWEEN_DOWNLOADS)
                else:
                    continue

            # Upload to Google Drive
            if os.path.exists(local_path):
                upload_image_to_drive(drive_service, local_path, drive_product_folder_id, filename)
                time.sleep(DELAY_BETWEEN_UPLOADS)

        # Generate Drive folder link
        drive_link = get_drive_folder_link(drive_product_folder_id) if drive_product_folder_id else ''

        print(f"  ✓ Completed: {downloaded_count}/{len(image_urls)} images processed")

        # Add result with Drive link
        result_row = row.to_dict()
        result_row['Google_Drive_Link'] = drive_link
        results.append(result_row)

    return pd.DataFrame(results)

if __name__ == "__main__":
    import sys

    # Get CSV file from command line argument or use default
    csv_file = sys.argv[1] if len(sys.argv) > 1 else "products_20250523_170643.csv"

    if not os.path.exists(csv_file):
        print(f"✗ CSV file not found: {csv_file}")
        print("Usage: uv run cactus-scrape-image.py [csv_file]")
        sys.exit(1)

    # Process images
    result_df = process_product_images(csv_file)

    if result_df is not None:
        # Save enhanced CSV
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_csv = f"products_with_drive_links_{timestamp}.csv"
        result_df.to_csv(output_csv, index=False)
        print(f"\n✓ Saved enhanced CSV: {output_csv}")

        # Setup Google Drive for CSV upload
        print("\n=== UPLOADING CSV TO GOOGLE DRIVE ===")
        drive_service = setup_google_drive()
        if drive_service:
            # Upload CSV to Google Drive
            csv_file_id = upload_csv_to_drive(drive_service, output_csv, DRIVE_FOLDER_ID)

            if csv_file_id:
                # Convert CSV to Google Sheet
                sheet_name = f"Products_with_Drive_Links_Sheet_{timestamp}"
                sheet_id = convert_csv_to_sheet(drive_service, csv_file_id, sheet_name)

                if sheet_id:
                    sheet_url = f"https://docs.google.com/spreadsheets/d/{sheet_id}"
                    print(f"✓ Google Sheet created: {sheet_url}")
                else:
                    print("✗ Failed to convert CSV to Google Sheet")
            else:
                print("✗ Failed to upload CSV to Google Drive")
        else:
            print("✗ Failed to setup Google Drive for CSV upload")

        print("\n=== IMAGE PROCESSING COMPLETED ===")
        print(f"📁 Local CSV: {output_csv}")
        print(f"📊 Enhanced CSV with Google Drive links saved")
        print(f"🔗 All product images uploaded to Google Drive")
    else:
        print("\n✗ Image processing failed")
