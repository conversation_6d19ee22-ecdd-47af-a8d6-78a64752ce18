import requests
import xmltodict
import pandas as pd
from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload, MediaIoBaseDownload
import io
import time
import os
import datetime

# Configuration
BASE_URL = "https://themoderncactusco.com"
GOOGLE_CREDS_PATH = os.getenv("GOOGLE_CREDS_PATH", "./cactusscrape-5d1e00101c40-google-creds.json")
DRIVE_FOLDER_ID = "1lXAYqTt8dfZzFyEQz0ttqZX1rVfEk2j_"

def setup_google_drive():
    """Authenticate and set up Google Drive client."""
    scopes = ["https://www.googleapis.com/auth/drive"]
    try:
        creds = Credentials.from_service_account_file(GOOGLE_CREDS_PATH, scopes=scopes)
    except Exception as e:
        print(f"Error loading credentials: {e}")
        return None
    try:
        drive_service = build("drive", "v3", credentials=creds)
    except Exception as e:
        print(f"Error initializing Drive API: {e}")
        return None
    return drive_service

def verify_google_drive_access(drive_service, folder_id):
    """Verify read/write access to Google Drive folder."""
    if not drive_service:
        print("Verification failed: No Drive service initialized.")
        return False
    try:
        results = drive_service.files().list(
            q=f"'{folder_id}' in parents and trashed=false",
            fields="files(id, name)"
        ).execute()
        files = results.get("files", [])
        print(f"Read access verified. Found {len(files)} files in folder:")
        for file in files:
            print(f"- {file['name']} (ID: {file['id']})")
    except Exception as e:
        print(f"Error listing files (read access): {e}")
        return False
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    test_filename = f"test_file_{timestamp}.txt"
    test_file_path = test_filename
    test_file_content = f"This is a test file to verify Google Drive write access. Created at {timestamp}."
    try:
        with open(test_file_path, "w") as f:
            f.write(test_file_content)
    except Exception as e:
        print(f"Error creating local test file: {e}")
        return False
    try:
        file_metadata = {
            "name": test_filename,
            "parents": [folder_id]
        }
        media = MediaFileUpload(test_file_path)
        uploaded_file = drive_service.files().create(
            body=file_metadata,
            media_body=media,
            fields="id, name"
        ).execute()
        print(f"Write access verified. Uploaded test file: {uploaded_file['name']} (ID: {uploaded_file['id']})")
        time.sleep(5)
        try:
            file = drive_service.files().get(fileId=uploaded_file["id"], fields="id, name, parents").execute()
            if file and file.get("id") == uploaded_file["id"]:
                print(f"File presence confirmed in folder. Name: {file.get('name')}")
            else:
                print("Error: Could not verify uploaded file.")
                return False
        except Exception as e:
            print(f"Error verifying file: {e}")
            return False
        try:
            drive_service.files().delete(fileId=uploaded_file["id"]).execute()
            print("Test file deleted successfully.")
        except Exception as e:
            print(f"Error deleting test file: {e}")
            return False
    except Exception as e:
        print(f"Error uploading test file (write access): {e}")
        return False
    finally:
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
            print("Local test file cleaned up.")
    return True

def convert_csv_to_sheet(drive_service, file_id, sheet_name):
    """Convert a CSV file on Drive to a Google Sheet with a new name."""
    try:
        file_metadata = {
            "name": sheet_name,
            "mimeType": "application/vnd.google-apps.spreadsheet"
        }
        updated_file = drive_service.files().copy(
            fileId=file_id,
            body=file_metadata,
            fields="id, name"
        ).execute()
        print(f"Converted CSV to Google Sheet: {updated_file['name']} (ID: {updated_file['id']})")
        return updated_file["id"]
    except Exception as e:
        print(f"Error converting CSV to Sheet: {e}")
        return None

def get_sitemap_urls():
    """Fetch URLs from parent sitemap and sub-sitemaps."""
    sitemap_urls = {"URL": [], "Type": []}
    print("\n=== SITEMAP PROCESSING ===")
    print(f"Fetching parent sitemap from {BASE_URL}/sitemap.xml...")
    try:
        response = requests.get(f"{BASE_URL}/sitemap.xml", timeout=10)
        response.raise_for_status()
        parent_sitemap = xmltodict.parse(response.text)
        sub_sitemaps = parent_sitemap["sitemapindex"]["sitemap"]
        print(f"Found {len(sub_sitemaps)} sub-sitemaps in parent sitemap")
    except Exception as e:
        print(f"Error fetching parent sitemap: {e}")
        return pd.DataFrame(sitemap_urls)

    product_count = 0
    for sitemap in sub_sitemaps:
        sitemap_url = sitemap["loc"]
        sitemap_type = "Unknown"
        if "products" in sitemap_url:
            sitemap_type = "Product"
        elif "pages" in sitemap_url:
            sitemap_type = "Page"
        elif "collections" in sitemap_url:
            sitemap_type = "Collection"
        elif "blogs" in sitemap_url:
            sitemap_type = "Blog"

        print(f"Processing {sitemap_type} sitemap: {sitemap_url}")
        try:
            response = requests.get(sitemap_url, timeout=10)
            response.raise_for_status()
            sub_sitemap = xmltodict.parse(response.text)
            urlset = sub_sitemap.get("urlset", {}).get("url", [])
            if isinstance(urlset, dict):
                urlset = [urlset]

            url_count = len(urlset)
            if sitemap_type == "Product":
                product_count += url_count

            print(f"  Found {url_count} URLs in {sitemap_type} sitemap")
            for urlobj in urlset:
                sitemap_urls["URL"].append(urlobj["loc"])
                sitemap_urls["Type"].append(sitemap_type)
            time.sleep(1)
        except Exception as e:
            print(f"Error fetching {sitemap_url}: {e}")

    total_urls = len(sitemap_urls["URL"])
    product_urls = sum(1 for t in sitemap_urls["Type"] if t == "Product")
    print(f"\nSitemap processing complete:")
    print(f"  Total URLs found: {total_urls}")
    print(f"  Product URLs found: {product_urls}")
    print("=== END SITEMAP PROCESSING ===\n")
    return pd.DataFrame(sitemap_urls)

def get_product_details():
    """Fetch product details from products.json."""
    product_data = {
        "Product_URL": [],
        "Title": [],
        "Description": [],
        "Price": [],
        "Image_URLs": []
    }

    print("\n=== PRODUCT DETAILS PROCESSING ===")
    print(f"Fetching products from {BASE_URL}/products.json...")

    # Shopify typically paginates results with a limit of 250 products per page
    # We'll implement pagination to get all products
    page = 1
    limit = 250
    all_products = []

    try:
        while True:
            url = f"{BASE_URL}/products.json?page={page}&limit={limit}"
            print(f"Fetching page {page} from {url}")

            response = requests.get(url, timeout=15)
            response.raise_for_status()

            data = response.json()
            products = data.get("products", [])

            if not products:
                print(f"No more products found on page {page}")
                break

            print(f"Found {len(products)} products on page {page}")
            all_products.extend(products)
            page += 1
            time.sleep(1)  # Be nice to the server

        print(f"Total products found: {len(all_products)}")

        # Process all products
        for i, product in enumerate(all_products):
            product_url = f"{BASE_URL}/products/{product['handle']}"
            title = product.get("title", "")
            description = product.get("body_html", "").replace("\n", " ").strip()

            # Handle potential missing variants
            if product.get("variants") and len(product["variants"]) > 0:
                price = product["variants"][0].get("price", "0.00")
            else:
                price = "0.00"
                print(f"Warning: No variants found for product: {title}")

            images = [img["src"] for img in product.get("images", [])]

            product_data["Product_URL"].append(product_url)
            product_data["Title"].append(title)
            product_data["Description"].append(description)
            product_data["Price"].append(price)
            product_data["Image_URLs"].append(",".join(images))

            # Print progress every 10 products
            if (i + 1) % 10 == 0:
                print(f"Processed {i + 1}/{len(all_products)} products")

    except Exception as e:
        print(f"Error fetching product details: {e}")
        import traceback
        traceback.print_exc()

    result_df = pd.DataFrame(product_data)
    print(f"Final product count in DataFrame: {len(result_df)}")
    print("=== END PRODUCT DETAILS PROCESSING ===\n")
    return result_df

def save_to_csv(df, filename):
    """Save DataFrame to CSV locally."""
    df.to_csv(filename, index=False, encoding="utf-8")
    return filename

def upload_to_google_drive(drive_service, filename, folder_id):
    """Upload file to Google Drive folder using Drive API."""
    try:
        file_metadata = {
            "name": filename,
            "parents": [folder_id]
        }
        media = MediaFileUpload(filename, mimetype="text/csv")
        uploaded_file = drive_service.files().create(
            body=file_metadata,
            media_body=media,
            fields="id, name"
        ).execute()
        print(f"Uploaded {filename} to Google Drive (ID: {uploaded_file['id']})")
        return uploaded_file["id"]
    except Exception as e:
        print(f"Error uploading {filename} to Google Drive: {e}")
        return None

def edit_csv_row(drive_service, file_id, product_url, new_price):
    """Download a CSV from Drive, edit a row, and re-upload."""
    try:
        request = drive_service.files().get_media(fileId=file_id)
        fh = io.BytesIO()
        downloader = MediaIoBaseDownload(fh, request)
        done = False
        while not done:
            status, done = downloader.next_chunk()
        fh.seek(0)
        df = pd.read_csv(fh)
        if "Product_URL" in df.columns and product_url in df["Product_URL"].values:
            df.loc[df["Product_URL"] == product_url, "Price"] = new_price
        else:
            print(f"Product URL {product_url} not found in CSV.")
            return False
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        modified_file = f"modified_products_{timestamp}.csv"
        df.to_csv(modified_file, index=False, encoding="utf-8")
        file_metadata = {
            "name": modified_file,
            "parents": [DRIVE_FOLDER_ID]
        }
        media = MediaFileUpload(modified_file, mimetype="text/csv")
        uploaded_file = drive_service.files().create(
            body=file_metadata,
            media_body=media,
            fields="id, name"
        ).execute()
        print(f"Uploaded modified CSV: {uploaded_file['name']} (ID: {uploaded_file['id']})")
        if os.path.exists(modified_file):
            os.remove(modified_file)
        return True
    except Exception as e:
        print(f"Error editing CSV: {e}")
        return False

def check_product_url_availability(url, timeout=5):
    """Check if a product URL is available on the website."""
    try:
        response = requests.head(url, timeout=timeout, allow_redirects=True)
        status = response.status_code
        is_available = 200 <= status < 300
        return {
            "URL": url,
            "Status": status,
            "Available": is_available
        }
    except Exception as e:
        return {
            "URL": url,
            "Status": "Error",
            "Available": False,
            "Error": str(e)
        }

def check_sample_urls(urls, sample_size=10):
    """Check availability for a sample of URLs."""
    print(f"\n=== CHECKING SAMPLE OF {min(sample_size, len(urls))} URLS ===")

    sample = urls[:sample_size] if len(urls) > sample_size else urls
    results = []

    for i, url in enumerate(sample):
        print(f"Checking URL {i+1}/{len(sample)}: {url}")
        result = check_product_url_availability(url)
        status_msg = "Available" if result["Available"] else f"Not Available (Status: {result['Status']})"
        print(f"  - {status_msg}")
        results.append(result)
        time.sleep(1)  # Be nice to the server

    available_count = sum(1 for r in results if r["Available"])
    print(f"\nResults: {available_count}/{len(results)} URLs are available")
    print("=== END URL CHECK ===\n")

    return results

def compare_sitemap_with_products(sitemap_df, product_df):
    """Compare sitemap URLs with product data to identify discrepancies."""
    print("\n=== COMPARING SITEMAP WITH PRODUCTS ===")

    # Filter sitemap for product URLs only
    product_urls_from_sitemap = sitemap_df[sitemap_df["Type"] == "Product"]["URL"].tolist()
    product_urls_from_json = product_df["Product_URL"].tolist()

    # Count statistics
    total_sitemap_products = len(product_urls_from_sitemap)
    total_json_products = len(product_urls_from_json)

    print(f"Total product URLs in sitemap: {total_sitemap_products}")
    print(f"Total products from products.json: {total_json_products}")

    # Find products in sitemap but not in products.json
    missing_from_json = [url for url in product_urls_from_sitemap if url not in product_urls_from_json]

    # Find products in products.json but not in sitemap
    missing_from_sitemap = [url for url in product_urls_from_json if url not in product_urls_from_sitemap]

    print(f"Products in sitemap but missing from products.json: {len(missing_from_json)}")
    if missing_from_json:
        print("Sample of missing products (first 10):")
        for url in missing_from_json[:10]:
            print(f"  - {url}")

        # Check if these URLs are actually available
        print("\nChecking if these missing products are actually available on the website...")
        availability_results = check_sample_urls(missing_from_json, 5)

        # If most URLs are available, there might be an issue with products.json
        available_count = sum(1 for r in availability_results if r["Available"])
        if available_count > len(availability_results) // 2:
            print("\nMost of the missing products are actually available on the website.")
            print("This suggests that products.json is not returning all products.")
            print("The site might be using pagination or have a limit on the API.")

    print(f"Products in products.json but missing from sitemap: {len(missing_from_sitemap)}")
    if missing_from_sitemap:
        print("Sample of products not in sitemap (first 10):")
        for url in missing_from_sitemap[:10]:
            print(f"  - {url}")

    # Create a DataFrame with the missing products for further analysis
    missing_products_df = pd.DataFrame({
        "URL": missing_from_json,
        "Source": ["Sitemap"] * len(missing_from_json)
    })

    print("=== END COMPARISON ===\n")
    return missing_products_df

def main():
    print("Setting up Google Drive client...")
    drive_service = setup_google_drive()
    if not drive_service:
        print("Failed to set up Google Drive client. Exiting.")
        return
    print("Verifying Google Drive access...")
    if not verify_google_drive_access(drive_service, DRIVE_FOLDER_ID):
        print("Google Drive API verification failed. Exiting.")
        return

    # Get sitemap data
    print("Generating sitemap...")
    sitemap_df = get_sitemap_urls()
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    sitemap_file = f"sitemap_{timestamp}.csv"
    save_to_csv(sitemap_df, sitemap_file)
    sitemap_file_id = upload_to_google_drive(drive_service, sitemap_file, DRIVE_FOLDER_ID)
    if sitemap_file_id:
        convert_csv_to_sheet(drive_service, sitemap_file_id, f"Sitemap_Sheet_{timestamp}")

    # Get product details
    print("Scraping product details...")
    product_df = get_product_details()
    product_file = f"products_{timestamp}.csv"
    save_to_csv(product_df, product_file)
    product_file_id = upload_to_google_drive(drive_service, product_file, DRIVE_FOLDER_ID)
    if product_file_id:
        convert_csv_to_sheet(drive_service, product_file_id, f"Products_Sheet_{timestamp}")

    # Compare sitemap with products.json data
    missing_products_df = compare_sitemap_with_products(sitemap_df, product_df)

    # Save missing products to CSV
    if not missing_products_df.empty:
        missing_file = f"missing_products_{timestamp}.csv"
        save_to_csv(missing_products_df, missing_file)
        missing_file_id = upload_to_google_drive(drive_service, missing_file, DRIVE_FOLDER_ID)
        if missing_file_id:
            convert_csv_to_sheet(drive_service, missing_file_id, f"Missing_Products_Sheet_{timestamp}")

    print("Script execution completed successfully.")

if __name__ == "__main__":
    main()
