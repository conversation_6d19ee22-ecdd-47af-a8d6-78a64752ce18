import gspread
from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
import os
import pandas as pd
import time
import datetime

# Configuration
GOOGLE_CREDS_PATH = os.getenv("GOOGLE_CREDS_PATH", "./cactusscrape-5d1e00101c40-google-creds.json")
DRIVE_FOLDER_URL = "https://drive.google.com/drive/folders/1lXAYqTt8dfZzFyEQz0ttqZX1rVfEk2j_"
DRIVE_FOLDER_ID = "1lXAYqTt8dfZzFyEQz0ttqZX1rVfEk2j_"  # Your folder ID

def setup_google_drive():
    """Authenticate and set up Google Drive client with verification."""
    scopes = [
        "https://www.googleapis.com/auth/spreadsheets",
        "https://www.googleapis.com/auth/drive"
    ]

    # Authenticate
    try:
        creds = Credentials.from_service_account_file(GOOGLE_CREDS_PATH, scopes=scopes)
    except Exception as e:
        print(f"Error loading credentials: {e}")
        return None

    # Initialize gspread client (for Sheets and Drive uploads)
    try:
        gspread_client = gspread.authorize(creds)
    except Exception as e:
        print(f"Error authorizing gspread client: {e}")
        return None

    # Initialize Google Drive API client
    try:
        drive_service = build("drive", "v3", credentials=creds)
    except Exception as e:
        print(f"Error initializing Drive API: {e}")
        return None

    return {"gspread_client": gspread_client, "drive_service": drive_service}

def verify_google_drive_access(clients, folder_id):
    """Verify read/write access to Google Drive folder."""
    if not clients:
        print("Verification failed: No clients initialized.")
        return False

    drive_service = clients["drive_service"]

    # Step 1: Test read access (list files in folder)
    try:
        results = drive_service.files().list(
            q=f"'{folder_id}' in parents and trashed=false",
            fields="files(id, name)"
        ).execute()
        files = results.get("files", [])
        print(f"Read access verified. Found {len(files)} files in folder:")
        for file in files:
            print(f"- {file['name']} (ID: {file['id']})")
    except Exception as e:
        print(f"Error listing files (read access): {e}")
        return False

    # Step 2: Test write access (create and verify a test file)
    # Add timestamp to make filename unique
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    test_filename = f"test_file_{timestamp}.txt"
    test_file_path = test_filename
    test_file_content = f"This is a test file to verify Google Drive write access. Created at {timestamp}."

    # Create a temporary test file locally
    try:
        with open(test_file_path, "w") as f:
            f.write(test_file_content)
    except Exception as e:
        print(f"Error creating local test file: {e}")
        return False

    # Upload test file to Google Drive
    try:
        file_metadata = {
            "name": test_filename,
            "parents": [folder_id]
        }
        media = MediaFileUpload(test_file_path)
        uploaded_file = drive_service.files().create(
            body=file_metadata,
            media_body=media,
            fields="id, name"
        ).execute()
        print(f"Write access verified. Uploaded test file: {uploaded_file['name']} (ID: {uploaded_file['id']})")

        # Verify the file exists
        file_id = uploaded_file["id"]

        # Add delay to allow Google Drive to process the upload
        print("Waiting for file to propagate in Google Drive...")
        time.sleep(5)  # Increased to 5 seconds

        # Verify file directly by ID instead of searching by name
        try:
            # Get file by ID to verify it exists
            file = drive_service.files().get(fileId=file_id, fields="id, name, parents").execute()
            if file and file.get("id") == file_id:
                print(f"File presence confirmed in folder. Name: {file.get('name')}")
            else:
                print("Error: Could not verify uploaded file.")
                return False
        except Exception as e:
            print(f"Error verifying file: {e}")
            return False

        # Optional: Delete the test file to keep the folder clean
        try:
            drive_service.files().delete(fileId=file_id).execute()
            print("Test file deleted successfully.")
        except Exception as e:
            print(f"Error deleting test file: {e}")
            return False
    except Exception as e:
        print(f"Error uploading test file (write access): {e}")
        return False
    finally:
        # Clean up local test file
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
            print("Local test file cleaned up.")

    return True

def main():
    # Set up Google Drive clients
    print("Setting up Google Drive clients...")
    clients = setup_google_drive()
    if not clients:
        print("Failed to set up Google Drive clients. Exiting.")
        return

    # Verify Google Drive access
    print("Verifying Google Drive access...")
    if verify_google_drive_access(clients, DRIVE_FOLDER_ID):
        print("Google Drive API verification successful! Ready to read/write.")
    else:
        print("Google Drive API verification failed. Check credentials and folder permissions.")
        return

    # Example: Upload a sample CSV to test integration (optional)
    print("Creating and uploading a sample CSV...")
    sample_data = pd.DataFrame({"Test": ["Sample data"]})
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    sample_file = f"sample_test_{timestamp}.csv"
    sample_data.to_csv(sample_file, index=False, encoding="utf-8")
    try:
        # Use the Drive API directly instead of gspread's upload_file
        file_metadata = {
            "name": sample_file,
            "parents": [DRIVE_FOLDER_ID]
        }
        media = MediaFileUpload(sample_file, mimetype="text/csv")
        uploaded_file = clients["drive_service"].files().create(
            body=file_metadata,
            media_body=media,
            fields="id, name"
        ).execute()
        print(f"Uploaded {sample_file} to Google Drive (ID: {uploaded_file['id']})")
    except Exception as e:
        print(f"Error uploading sample file: {e}")
    finally:
        if os.path.exists(sample_file):
            os.remove(sample_file)
            print("Local sample file cleaned up.")

if __name__ == "__main__":
    main()
