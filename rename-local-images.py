#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to rename local image files to include product name prefix.

This script will:
1. Scan the local 'images' directory
2. For each product folder, rename files from "image_<index>.<ext>" to "<product_name>_<index>.<ext>"
3. Provide detailed logging of all rename operations
4. Skip files that are already properly named
"""

import os
import re
from pathlib import Path

# Configuration
IMAGES_BASE_DIR = "images"

def is_old_format(filename):
    """Check if filename follows the old 'image_<index>.<ext>' format."""
    pattern = r'^image_\d+\.[a-zA-Z0-9]+$'
    return bool(re.match(pattern, filename))

def extract_index_and_extension(filename):
    """Extract index and extension from old format filename."""
    # Pattern: image_<index>.<ext>
    match = re.match(r'^image_(\d+)\.([a-zA-Z0-9]+)$', filename)
    if match:
        return match.group(1), match.group(2)
    return None, None

def rename_images_in_product_folder(product_folder_path, product_name):
    """Rename all images in a product folder to include product name prefix."""
    renamed_count = 0
    skipped_count = 0
    error_count = 0
    
    print(f"\n📁 Processing folder: {product_name}")
    
    # Get all files in the product folder
    try:
        files = [f for f in os.listdir(product_folder_path) if os.path.isfile(os.path.join(product_folder_path, f))]
    except Exception as e:
        print(f"  ✗ Error reading folder: {e}")
        return 0, 0, 1
    
    if not files:
        print(f"  ⚠ No files found in folder")
        return 0, 0, 0
    
    print(f"  Found {len(files)} files")
    
    for filename in files:
        old_path = os.path.join(product_folder_path, filename)
        
        # Check if file is in old format
        if not is_old_format(filename):
            print(f"    ⏭ Skipping '{filename}' (already in new format or not an image)")
            skipped_count += 1
            continue
        
        # Extract index and extension
        index, extension = extract_index_and_extension(filename)
        if not index or not extension:
            print(f"    ✗ Could not parse '{filename}'")
            error_count += 1
            continue
        
        # Generate new filename
        new_filename = f"{product_name}_{index}.{extension}"
        new_path = os.path.join(product_folder_path, new_filename)
        
        # Check if new filename already exists
        if os.path.exists(new_path):
            print(f"    ⚠ Target file '{new_filename}' already exists, skipping")
            skipped_count += 1
            continue
        
        # Perform the rename
        try:
            os.rename(old_path, new_path)
            print(f"    ✓ Renamed: '{filename}' → '{new_filename}'")
            renamed_count += 1
        except Exception as e:
            print(f"    ✗ Error renaming '{filename}': {e}")
            error_count += 1
    
    return renamed_count, skipped_count, error_count

def scan_and_rename_all_images():
    """Scan all product folders and rename images."""
    print("🔄 Starting local image renaming process...")
    print(f"📂 Base directory: {IMAGES_BASE_DIR}")
    
    if not os.path.exists(IMAGES_BASE_DIR):
        print(f"✗ Images directory '{IMAGES_BASE_DIR}' not found!")
        return
    
    # Get all product folders
    try:
        product_folders = [d for d in os.listdir(IMAGES_BASE_DIR) 
                          if os.path.isdir(os.path.join(IMAGES_BASE_DIR, d))]
    except Exception as e:
        print(f"✗ Error reading images directory: {e}")
        return
    
    if not product_folders:
        print("⚠ No product folders found in images directory")
        return
    
    print(f"📊 Found {len(product_folders)} product folders")
    
    total_renamed = 0
    total_skipped = 0
    total_errors = 0
    
    for i, product_folder in enumerate(product_folders, 1):
        product_folder_path = os.path.join(IMAGES_BASE_DIR, product_folder)
        
        print(f"\n[{i}/{len(product_folders)}] Processing: {product_folder}")
        
        renamed, skipped, errors = rename_images_in_product_folder(product_folder_path, product_folder)
        
        total_renamed += renamed
        total_skipped += skipped
        total_errors += errors
        
        print(f"  📈 Results: {renamed} renamed, {skipped} skipped, {errors} errors")
    
    # Final summary
    print(f"\n{'='*60}")
    print(f"🎉 RENAMING COMPLETE!")
    print(f"📊 Final Summary:")
    print(f"   ✓ Files renamed: {total_renamed}")
    print(f"   ⏭ Files skipped: {total_skipped}")
    print(f"   ✗ Errors: {total_errors}")
    print(f"   📁 Folders processed: {len(product_folders)}")
    print(f"{'='*60}")

def preview_changes():
    """Preview what changes would be made without actually renaming files."""
    print("👀 PREVIEW MODE - No files will be renamed")
    print(f"📂 Scanning directory: {IMAGES_BASE_DIR}")
    
    if not os.path.exists(IMAGES_BASE_DIR):
        print(f"✗ Images directory '{IMAGES_BASE_DIR}' not found!")
        return
    
    try:
        product_folders = [d for d in os.listdir(IMAGES_BASE_DIR) 
                          if os.path.isdir(os.path.join(IMAGES_BASE_DIR, d))]
    except Exception as e:
        print(f"✗ Error reading images directory: {e}")
        return
    
    total_to_rename = 0
    
    for product_folder in product_folders:
        product_folder_path = os.path.join(IMAGES_BASE_DIR, product_folder)
        
        try:
            files = [f for f in os.listdir(product_folder_path) 
                    if os.path.isfile(os.path.join(product_folder_path, f))]
        except Exception as e:
            print(f"✗ Error reading folder {product_folder}: {e}")
            continue
        
        files_to_rename = [f for f in files if is_old_format(f)]
        
        if files_to_rename:
            print(f"\n📁 {product_folder}:")
            for filename in files_to_rename:
                index, extension = extract_index_and_extension(filename)
                if index and extension:
                    new_filename = f"{product_folder}_{index}.{extension}"
                    print(f"    '{filename}' → '{new_filename}'")
                    total_to_rename += 1
    
    print(f"\n📊 Total files that would be renamed: {total_to_rename}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--preview":
        preview_changes()
    else:
        print("🚀 Local Image Renaming Script")
        print("=" * 40)
        print("This script will rename image files from 'image_<index>.<ext>' to '<product_name>_<index>.<ext>'")
        print("Run with --preview to see what changes would be made without actually renaming files")
        print()
        
        response = input("Do you want to proceed with renaming? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            scan_and_rename_all_images()
        else:
            print("❌ Operation cancelled")
