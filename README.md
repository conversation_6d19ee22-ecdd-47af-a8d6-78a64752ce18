# Cactus Scraping & Image Management Tools

This repository contains a complete suite of Python scripts for scraping product data from The Modern Cactus Co. website and managing product images both locally and on Google Drive.

## 📁 Scripts Overview

### Data Scraping Scripts

#### 1. `cactus-scrape.py`

Main scraping script that extracts product data from The Modern Cactus Co. website.

- Fetches sitemap data from all sub-sitemaps
- Scrapes product details from products.json API
- Compares sitemap vs products.json to find discrepancies
- Uploads results to Google Drive as CSV and Google Sheets

#### 2. `cactus-scrape-image.py`

Image downloading and organization script.

- Downloads product images from scraped URLs
- Organizes images into product-specific folders
- Uploads images to Google Drive with proper folder structure
- Creates enhanced CSV with Google Drive links

### Image Renaming Scripts

#### 3. `rename-local-images.py`

Renames image files in the local `images/` directory.

#### 4. `rename-drive-images.py`

Renames image files in Google Drive within the `images/` folder.

## 🎯 What These Scripts Do

**Before:**

```
images/Natural_Palm_Leaf_Crown_Top_Brim_Hat/
├── image_1.png
├── image_2.jpg
└── image_3.webp
```

**After:**

```
images/Natural_Palm_Leaf_Crown_Top_Brim_Hat/
├── Natural_Palm_Leaf_Crown_Top_Brim_Hat_1.png
├── Natural_Palm_Leaf_Crown_Top_Brim_Hat_2.jpg
└── Natural_Palm_Leaf_Crown_Top_Brim_Hat_3.webp
```

## 🚀 Quick Start

### Prerequisites

1. **Python 3.7+** installed
2. **uv** package manager installed
3. For Google Drive script: Google Drive API credentials

### Installation

1. Clone or download this repository
2. Install dependencies:

   ```bash
   uv sync
   ```

### For Google Drive Script Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google Drive API
4. Create credentials (Service Account or OAuth 2.0)
5. Download the credentials file as `credentials.json`
6. Place `credentials.json` in the project root directory

## 📖 Usage

### Data Scraping

#### Scrape Product Data

```bash
# Scrape all product data and upload to Google Drive
uv run cactus-scrape.py
```

#### Download Product Images

```bash
# Download images using a specific CSV file
uv run cactus-scrape-image.py products_20250523_170643.csv

# Or use the default CSV file
uv run cactus-scrape-image.py
```

### Image Renaming

#### Local Images

```bash
# Preview changes (recommended first)
uv run rename-local-images.py --preview

# Perform actual renaming
uv run rename-local-images.py
```

#### Google Drive Images

```bash
# Preview changes (recommended first)
uv run rename-drive-images.py --preview

# Perform actual renaming
uv run rename-drive-images.py
```

## 🔧 Using `uv run` Commands

This project uses `uv` as the package manager. Here are all the available commands:

### Data Collection Workflow

```bash
# Step 1: Scrape product data from website
uv run cactus-scrape.py

# Step 2: Download product images (use the CSV from step 1)
uv run cactus-scrape-image.py products_YYYYMMDD_HHMMSS.csv

# Step 3: Rename images locally (optional)
uv run rename-local-images.py --preview  # Preview first
uv run rename-local-images.py            # Execute

# Step 4: Rename images on Google Drive (optional)
uv run rename-drive-images.py --preview  # Preview first
uv run rename-drive-images.py            # Execute
```

### Individual Script Usage

```bash
# Data scraping
uv run cactus-scrape.py                    # Full product data scrape

# Image downloading
uv run cactus-scrape-image.py              # Use default CSV
uv run cactus-scrape-image.py [csv_file]   # Use specific CSV

# Local image renaming
uv run rename-local-images.py              # Rename all local images
uv run rename-local-images.py --preview    # Preview changes only

# Google Drive image renaming
uv run rename-drive-images.py              # Rename all Drive images
uv run rename-drive-images.py --preview    # Preview changes only
```

## ✨ Features

### Data Scraping Features

#### 🌐 Comprehensive Website Scraping

- **Sitemap Processing**: Fetches and processes all sub-sitemaps (products, pages, collections, blogs)
- **Product API Integration**: Uses Shopify's products.json API with pagination support
- **Data Comparison**: Compares sitemap URLs vs API data to identify discrepancies
- **URL Validation**: Checks sample URLs for availability to verify data accuracy

#### 📊 Data Export & Storage

- **Multiple Formats**: Saves data as CSV and converts to Google Sheets automatically
- **Google Drive Integration**: Uploads all results to Google Drive with organized folder structure
- **Timestamped Files**: All outputs include timestamps for version tracking
- **Enhanced Data**: Adds Google Drive links to product data for easy access

#### 🖼️ Image Management

- **Bulk Image Download**: Downloads all product images with retry logic and error handling
- **Smart Organization**: Creates product-specific folders both locally and on Google Drive
- **Filename Standardization**: Uses product names as prefixes for better organization
- **Duplicate Prevention**: Skips already downloaded/uploaded files to save time

### Image Renaming Features

#### 🔍 Smart Detection

- Only renames files matching the pattern `image_<number>.<extension>`
- Skips files already in the correct format
- Supports common image formats: `.jpg`, `.jpeg`, `.png`, `.webp`, `.gif`, `.bmp`, `.tiff`

### 📄 Preview Mode

- Use `--preview` flag to see what changes would be made
- No actual files are modified in preview mode
- Perfect for testing before running the actual rename

### 🔄 Resume Capability

- Scripts can be run multiple times safely
- Already renamed files are automatically skipped
- Useful for processing new files added later

### 📊 Progress Tracking

- Real-time progress updates
- Detailed summary at completion
- Error reporting with specific file information

### 🌐 Google Drive Pagination

- Handles Google Drive API pagination automatically
- Processes all folders regardless of quantity
- Rate limiting to respect API limits

## 📋 Example Output

```
🚀 Local Image Renaming Script
========================================
📊 Found 318 product folders

[1/318] Processing: Natural_Palm_Leaf_Crown_Top_Brim_Hat
📁 Processing folder: Natural_Palm_Leaf_Crown_Top_Brim_Hat
  Found 3 files
    🔄 Renaming: 'image_1.png' → 'Natural_Palm_Leaf_Crown_Top_Brim_Hat_1.png'
    ✓ Successfully renamed
    🔄 Renaming: 'image_2.jpg' → 'Natural_Palm_Leaf_Crown_Top_Brim_Hat_2.jpg'
    ✓ Successfully renamed
    🔄 Renaming: 'image_3.webp' → 'Natural_Palm_Leaf_Crown_Top_Brim_Hat_3.webp'
    ✓ Successfully renamed
  📈 Results: 3 renamed, 0 skipped, 0 errors

============================================================
🎉 RENAMING COMPLETE!
📊 Final Summary:
   ✓ Files renamed: 899
   ⏭ Files skipped: 0
   ✗ Errors: 0
   📁 Folders processed: 318
============================================================
```

## 🛡️ Safety Features

### File Protection

- Never overwrites existing files
- Comprehensive error handling
- Detailed logging of all operations

### Validation

- Validates file extensions before renaming
- Checks for proper folder structure
- Ensures target filenames are valid

### Recovery

- All operations are logged
- Failed operations don't affect other files
- Scripts can be safely interrupted and resumed

## 🔧 Technical Details

### Dependencies

#### Core Dependencies

- `requests` - HTTP requests for web scraping
- `pandas` - Data manipulation and CSV handling
- `xmltodict` - XML parsing for sitemap processing
- `pathlib` - File system operations
- `re` - Pattern matching for filename validation

#### Google Drive Integration

- `google-api-python-client` - Google Drive API access
- `google-auth` - Google authentication
- `google-oauth2` - OAuth2 service account credentials

#### Image Processing

- `urllib.parse` - URL parsing for image downloads
- `io` - File I/O operations
- `time` - Rate limiting and delays

### File Structure Expected

```
project-root/
├── images/                          # Local images directory
│   ├── Product_Name_1/
│   │   ├── image_1.jpg
│   │   └── image_2.png
│   └── Product_Name_2/
│       └── image_1.webp
├── credentials.json                 # Google Drive credentials (rename from your file)
├── cactusscrape-5d1e00101c40-google-creds.json  # Google Drive service account
├── cactus-scrape.py                 # Main scraping script
├── cactus-scrape-image.py           # Image downloading script
├── rename-local-images.py           # Local image renaming
├── rename-drive-images.py           # Google Drive image renaming
├── pyproject.toml                   # uv project configuration
└── README.md
```

### Google Drive Structure Expected

```
Google Drive/
└── images/                          # Google Drive images folder
    ├── Product_Name_1/
    │   ├── image_1.jpg
    │   └── image_2.png
    └── Product_Name_2/
        └── image_1.webp
```

## 🚨 Important Notes

1. **Backup First**: Always backup your files before running these scripts
2. **Test with Preview**: Use `--preview` mode first to verify expected changes
3. **Google Drive Limits**: The Google Drive script includes rate limiting but may still hit API quotas with very large datasets
4. **Credentials Security**: Keep your `credentials.json` file secure and never commit it to version control

## 📊 Performance

### Local Script

- Processes ~300 folders with ~900 files in under 30 seconds
- Memory efficient - processes one folder at a time

### Google Drive Script

- Processes ~300 folders with ~600 files in 5-10 minutes
- Includes API rate limiting for reliability
- Automatic pagination handles any number of folders

## 🤝 Contributing

Feel free to submit issues, feature requests, or pull requests to improve these scripts.

## 📄 License

This project is open source and available under the MIT License.
