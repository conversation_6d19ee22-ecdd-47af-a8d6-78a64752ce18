# Image Renaming Scripts

This repository contains two Python scripts designed to rename product image files from a generic `image_<index>.<ext>` format to a more descriptive `<product_name>_<index>.<ext>` format.

## 📁 Scripts Overview

### 1. `rename-local-images.py`

Renames image files in the local `images/` directory.

### 2. `rename-drive-images.py`

Renames image files in Google Drive within the `images/` folder.

## 🎯 What These Scripts Do

**Before:**

```
images/Natural_Palm_Leaf_Crown_Top_Brim_Hat/
├── image_1.png
├── image_2.jpg
└── image_3.webp
```

**After:**

```
images/Natural_Palm_Leaf_Crown_Top_Brim_Hat/
├── Natural_Palm_Leaf_Crown_Top_Brim_Hat_1.png
├── Natural_Palm_Leaf_Crown_Top_Brim_Hat_2.jpg
└── Natural_Palm_Leaf_Crown_Top_Brim_Hat_3.webp
```

## 🚀 Quick Start

### Prerequisites

1. **Python 3.7+** installed
2. **uv** package manager installed
3. For Google Drive script: Google Drive API credentials

### Installation

1. Clone or download this repository
2. Install dependencies:

   ```bash
   uv sync
   ```

### For Google Drive Script Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google Drive API
4. Create credentials (Service Account or OAuth 2.0)
5. Download the credentials file as `credentials.json`
6. Place `credentials.json` in the project root directory

## 📖 Usage

### Local Images

```bash
# Preview changes (recommended first)
uv run rename-local-images.py --preview

# Perform actual renaming
uv run rename-local-images.py
```

### Google Drive Images

```bash
# Preview changes (recommended first)
uv run rename-drive-images.py --preview

# Perform actual renaming
uv run rename-drive-images.py
```

## ✨ Features

### 🔍 Smart Detection

- Only renames files matching the pattern `image_<number>.<extension>`
- Skips files already in the correct format
- Supports common image formats: `.jpg`, `.jpeg`, `.png`, `.webp`, `.gif`, `.bmp`, `.tiff`

### 📄 Preview Mode

- Use `--preview` flag to see what changes would be made
- No actual files are modified in preview mode
- Perfect for testing before running the actual rename

### 🔄 Resume Capability

- Scripts can be run multiple times safely
- Already renamed files are automatically skipped
- Useful for processing new files added later

### 📊 Progress Tracking

- Real-time progress updates
- Detailed summary at completion
- Error reporting with specific file information

### 🌐 Google Drive Pagination

- Handles Google Drive API pagination automatically
- Processes all folders regardless of quantity
- Rate limiting to respect API limits

## 📋 Example Output

```
🚀 Local Image Renaming Script
========================================
📊 Found 318 product folders

[1/318] Processing: Natural_Palm_Leaf_Crown_Top_Brim_Hat
📁 Processing folder: Natural_Palm_Leaf_Crown_Top_Brim_Hat
  Found 3 files
    🔄 Renaming: 'image_1.png' → 'Natural_Palm_Leaf_Crown_Top_Brim_Hat_1.png'
    ✓ Successfully renamed
    🔄 Renaming: 'image_2.jpg' → 'Natural_Palm_Leaf_Crown_Top_Brim_Hat_2.jpg'
    ✓ Successfully renamed
    🔄 Renaming: 'image_3.webp' → 'Natural_Palm_Leaf_Crown_Top_Brim_Hat_3.webp'
    ✓ Successfully renamed
  📈 Results: 3 renamed, 0 skipped, 0 errors

============================================================
🎉 RENAMING COMPLETE!
📊 Final Summary:
   ✓ Files renamed: 899
   ⏭ Files skipped: 0
   ✗ Errors: 0
   📁 Folders processed: 318
============================================================
```

## 🛡️ Safety Features

### File Protection

- Never overwrites existing files
- Comprehensive error handling
- Detailed logging of all operations

### Validation

- Validates file extensions before renaming
- Checks for proper folder structure
- Ensures target filenames are valid

### Recovery

- All operations are logged
- Failed operations don't affect other files
- Scripts can be safely interrupted and resumed

## 🔧 Technical Details

### Dependencies

- `pathlib` - File system operations
- `re` - Pattern matching for filename validation
- `google-api-python-client` - Google Drive API access (Drive script only)
- `google-auth` - Google authentication (Drive script only)

### File Structure Expected

```
project-root/
├── images/                          # Local images directory
│   ├── Product_Name_1/
│   │   ├── image_1.jpg
│   │   └── image_2.png
│   └── Product_Name_2/
│       └── image_1.webp
├── credentials.json                 # Google Drive credentials
├── rename-local-images.py
├── rename-drive-images.py
└── README.md
```

### Google Drive Structure Expected

```
Google Drive/
└── images/                          # Google Drive images folder
    ├── Product_Name_1/
    │   ├── image_1.jpg
    │   └── image_2.png
    └── Product_Name_2/
        └── image_1.webp
```

## 🚨 Important Notes

1. **Backup First**: Always backup your files before running these scripts
2. **Test with Preview**: Use `--preview` mode first to verify expected changes
3. **Google Drive Limits**: The Google Drive script includes rate limiting but may still hit API quotas with very large datasets
4. **Credentials Security**: Keep your `credentials.json` file secure and never commit it to version control

## 📊 Performance

### Local Script

- Processes ~300 folders with ~900 files in under 30 seconds
- Memory efficient - processes one folder at a time

### Google Drive Script

- Processes ~300 folders with ~600 files in 5-10 minutes
- Includes API rate limiting for reliability
- Automatic pagination handles any number of folders

## 🤝 Contributing

Feel free to submit issues, feature requests, or pull requests to improve these scripts.

## 📄 License

This project is open source and available under the MIT License.
