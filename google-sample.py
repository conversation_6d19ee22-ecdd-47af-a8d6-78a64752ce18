import os
GOOGLE_CREDS_PATH = os.getenv("GOOGLE_CREDS_PATH", "/cactusscrape-5d1e00101c40-google-creds.json")

DRIVE_FOLDER_URL = "https://drive.google.com/drive/folders/1lXAYqTt8dfZzFyEQz0ttqZX1rVfEk2j_"
DRIVE_FOLDER_ID = "1lXAYqTt8dfZzFyEQz0ttqZX1rVfEk2j_"  # Your folder ID

def setup_google_drive():
    scopes = [
        "https://www.googleapis.com/auth/spreadsheets",
        "https://www.googleapis.com/auth/drive"
    ]
    creds = Credentials.from_service_account_file(GOOGLE_CREDS_PATH, scopes=scopes)
    client = gspread.authorize(creds)
    return client
